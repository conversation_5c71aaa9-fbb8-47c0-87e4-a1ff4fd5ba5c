package com.notemess.feature.contact.entity

import androidx.room.DatabaseView
import androidx.room.Embedded
import com.notemess.feature.db.entity.DatabaseUserEntity

@DatabaseView(
    viewName = "contacts_with_user",
    value = """
        SELECT contacts.*, users.* FROM contacts contacts
        LEFT JOIN users users ON contacts.contactUserId = users.userId
    """
)
data class DatabaseContactWithUser(
    @Embedded
    val contact: DatabaseContactEntity,
    @Embedded
    val user: DatabaseUserEntity?
)