package com.notemess.feature.contact

import com.notemess.feature.contact.entity.DatabaseContactEntity
import com.notemess.feature.contact.entity.DatabaseContactWithUser
import com.notemess.feature.contact.model.Contact
import com.notemess.feature.db.DatabaseUserMapper

object DatabaseContactMapper {

    fun toDatabaseEntity(contact: Contact): DatabaseContactEntity {
        return DatabaseContactEntity(
            contactId = contact.id,
            accountId = contact.accountId,
            contactUserId = contact.userId
        )
    }

    fun toDomain(entity: DatabaseContactEntity): Contact {
        return Contact(
            id = entity.contactId,
            accountId = entity.accountId,
            userId = entity.contactUserId
        )
    }

    fun toDomain(entity: DatabaseContactWithUser): Contact {
        return Contact(
            id = entity.contact.contactId,
            accountId = entity.contact.accountId,
            userId = entity.contact.contactUserId,
            user = entity.user?.let(DatabaseUserMapper::toDomain)
        )
    }
}