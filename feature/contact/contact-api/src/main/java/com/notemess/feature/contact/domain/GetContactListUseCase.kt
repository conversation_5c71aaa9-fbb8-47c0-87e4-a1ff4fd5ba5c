package com.notemess.feature.contact.domain

import com.notemess.feature.contact.model.Contact
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

interface GetContactListUseCase {

    operator fun invoke(): Flow<Result<List<Contact.Full>>>

    companion object {
        val Empty = object : GetContactListUseCase {
            override fun invoke(): Flow<Result<List<Contact.Full>>> {
                return flowOf(Result.success(emptyList()))
            }
        }
    }
}
