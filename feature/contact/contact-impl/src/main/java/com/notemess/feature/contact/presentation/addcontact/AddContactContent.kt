@file:OptIn(ExperimentalMaterial3Api::class)

package com.notemess.feature.contact.presentation.addcontact

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.autofill.ContentType
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentType
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.notemess.core.feature.FeatureContent
import com.notemess.core.ui.feature.ComposeFeatureContent
import com.notemess.core.ui.foundation.AppTheme
import com.notemess.core.ui.preview.PreviewSandbox
import com.notemess.core.ui.preview.injectPreviewComponent
import com.notemess.feature.contact.contactFeatureModule
import com.notemess.feature.contact.mockModule
import com.notemess.feature.task.member.R
import com.notemess.feature.user.User
import com.notemess.feature.user.UserId
import com.notemess.feature.user.UserSearchType

internal class AddContactContent(
    private val component: AddContactComponent
) : ComposeFeatureContent {

    @Composable
    override fun Composable(modifier: Modifier) {
        AddContactContent(
            modifier = modifier,
            component = component
        )
    }

    class Factory : FeatureContent.Factory<AddContactComponent> {
        override fun create(component: AddContactComponent): FeatureContent {
            return AddContactContent(component)
        }
    }
}

@Composable
private fun AddContactContent(
    modifier: Modifier = Modifier,
    component: AddContactComponent
) {
    val state by component.state.collectAsStateWithLifecycle()
    val foundUser = state.foundUser

    Column(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        QueryBlock(
            modifier = Modifier.fillMaxWidth(),
            types = state.searchTypes,
            selectedType = state.selectedSearchType,
            onTypeSelected = component::onSearchTypeChanged,
            query = state.searchQueryField,
            onQueryChanged = component::onQueryChanged,
            onKeyboardDone = component::onKeyboardDone
        )

        if (foundUser != null) {
            FoundUserBlock(
                modifier = Modifier.fillMaxWidth(),
                user = foundUser
            )
        }

        if (foundUser != null) {
            AddContactButton(
                modifier = Modifier.fillMaxWidth(),
                isLoading = state.isLoading,
                onClick = component::onAddContactClicked
            )
        } else {
            SearchButton(
                modifier = Modifier.fillMaxWidth(),
                isLoading = state.isLoading,
                isEnabled = state.isSearchButtonEnabled,
                onClick = component::onSearchClicked
            )
        }
    }
}

@Suppress("LongParameterList")
@Composable
private fun QueryBlock(
    modifier: Modifier = Modifier,
    types: List<UserSearchType>,
    selectedType: UserSearchType,
    onTypeSelected: (UserSearchType) -> Unit,
    query: String,
    onQueryChanged: (String) -> Unit,
    onKeyboardDone: () -> Unit
) {

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        UserSearchTypeSelector(
            modifier = Modifier.fillMaxWidth(),
            types = types,
            selectedType = selectedType,
            onTypeSelected = onTypeSelected
        )

        // Поле ввода поискового запроса
        OutlinedTextField(
            modifier = Modifier
                .fillMaxWidth()
                .semantics {
                    contentType = when (selectedType) {
                        UserSearchType.Email -> ContentType.EmailAddress
                        UserSearchType.Name -> ContentType.PersonFullName
                    }
                },
            value = query,
            onValueChange = onQueryChanged,
            placeholder = {
                Text(
                    text = selectedType.getPlaceholder(),
                    style = MaterialTheme.typography.bodyLarge
                )
            },
            shape = MaterialTheme.shapes.medium,
            singleLine = true,
            keyboardActions = KeyboardActions { onKeyboardDone() },
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done
            )
        )
    }
}

@Composable
private fun UserSearchTypeSelector(
    modifier: Modifier,
    types: List<UserSearchType>,
    selectedType: UserSearchType,
    onTypeSelected: (UserSearchType) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.add_contact_search_by_label),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface
        )

        ExposedDropdownMenuBox(
            modifier = Modifier.weight(1f),
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            OutlinedTextField(
                modifier = Modifier
                    .menuAnchor(MenuAnchorType.PrimaryNotEditable)
                    .fillMaxWidth(),
                value = selectedType.getDisplayName(),
                onValueChange = { },
                readOnly = true,
                trailingIcon = {
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                },
                colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors(),
                shape = MaterialTheme.shapes.medium
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                types.forEach { type ->
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = type.getDisplayName(),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        },
                        onClick = {
                            onTypeSelected(type)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun UserSearchType.getDisplayName(): String {
    return when (this) {
        UserSearchType.Email -> stringResource(R.string.add_contact_search_type_email)
        UserSearchType.Name -> stringResource(R.string.add_contact_search_type_name)
    }
}

@Composable
private fun UserSearchType.getPlaceholder(): String {
    return when (this) {
        UserSearchType.Email -> stringResource(R.string.add_contact_search_type_email)
        UserSearchType.Name -> stringResource(R.string.add_contact_search_type_name)
    }
}

@Composable
private fun FoundUserBlock(
    modifier: Modifier = Modifier,
    user: User
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            modifier = Modifier.padding(horizontal = 16.dp),
            text = stringResource(R.string.add_contact_user_found_title).uppercase()
        )

        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            color = AppTheme.colorScheme.material.onSurfaceVariant,
                            shape = CircleShape
                        ),
                    text = user.displayName.firstOrNull { it.isLetter() }.toString(),
                    textAlign = TextAlign.Center,
                    color = AppTheme.colorScheme.material.surfaceContainer
                )

                Text(
                    text = user.displayName
                )
            }
        }
    }
}

@Composable
private fun AddContactButton(
    modifier: Modifier = Modifier,
    isLoading: Boolean,
    onClick: () -> Unit
) {
    AnimatedContent(
        modifier = modifier,
        targetState = isLoading
    ) { isLoading ->
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp)
            )
        } else {
            TextButton(
                modifier = modifier,
                onClick = onClick
            ) {
                Text(
                    text = stringResource(R.string.add_contact_button)
                )
            }
        }
    }
}

@Composable
private fun SearchButton(
    modifier: Modifier = Modifier,
    isLoading: Boolean,
    isEnabled: Boolean,
    onClick: () -> Unit
) {
    AnimatedContent(
        modifier = modifier,
        targetState = isLoading
    ) { isLoading ->
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp)
            )
        } else {
            TextButton(
                enabled = isEnabled,
                onClick = onClick
            ) {
                Text(
                    text = stringResource(R.string.add_contact_search_button)
                )
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun AddContactContentPreview(
    @PreviewParameter(AddContactUiStatePreviewProvider::class)
    state: AddContactUiState
) = PreviewSandbox(
    application = { modules(contactFeatureModule, mockModule) }
) {
    Surface {
        AddContactContent(
            component = injectPreviewComponent(state)
        )
    }
}

private class AddContactUiStatePreviewProvider : PreviewParameterProvider<AddContactUiState> {
    override val values = sequenceOf(
        AddContactUiState.Default.copy(
            foundUser = null,
            isLoading = true
        ),

        AddContactUiState.Default.copy(
            foundUser = User(
                id = UserId("1"),
                displayName = "Denis Tkachenko",
            ),
        ),

        AddContactUiState.Default.copy(
            error = IllegalStateException("preview error")
        )
    )
}

