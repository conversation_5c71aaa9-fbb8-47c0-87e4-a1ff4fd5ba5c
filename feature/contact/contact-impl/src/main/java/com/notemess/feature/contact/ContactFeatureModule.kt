package com.notemess.feature.contact

import com.notemess.core.feature.FeatureEntryPoint
import com.notemess.core.ui.decompose.DecomposeFeatureEntryPoint
import com.notemess.feature.contact.data.ContactRepositoryImpl
import com.notemess.feature.contact.data.source.LocalContactDataSource
import com.notemess.feature.contact.data.source.RemoteContactDataSource
import com.notemess.feature.contact.data.source.db.DatabaseContactDataSource
import com.notemess.feature.contact.data.source.firebase.FirebaseContactDataSource
import com.notemess.feature.contact.domain.ContactRepository
import com.notemess.feature.contact.domain.GetContactListUseCase
import com.notemess.feature.contact.domain.usecase.GetContactListUseCaseImpl
import com.notemess.feature.contact.presentation.addcontact.AddContactComponent
import com.notemess.feature.contact.presentation.addcontact.AddContactComponentImpl
import com.notemess.feature.contact.presentation.addcontact.AddContactContent
import com.notemess.feature.user.usecase.SearchUserUseCase
import org.koin.core.module.Module
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.onOptions
import org.koin.core.module.dsl.singleOf

import org.koin.dsl.bind
import org.koin.dsl.module

public val contactFeatureModule: Module = module {
    singleOf(::DatabaseContactDataSource).bind<LocalContactDataSource>()
    singleOf(::FirebaseContactDataSource).bind<RemoteContactDataSource>()
    singleOf(::ContactRepositoryImpl).bind<ContactRepository>()

    factoryOf(::GetContactListUseCaseImpl).bind<GetContactListUseCase>()

    factory {
        DecomposeFeatureEntryPoint(
            id = AddContactScreen.id,
            featureClass = AddContactScreen::class,
            componentClass = AddContactComponent::class,
            componentFactory = get(),
            contentFactory = AddContactContent.Factory()
        )
    }.onOptions { bind<FeatureEntryPoint<*>>() }

    factoryOf(::AddContactComponentImpl).bind<AddContactComponent>()
}

internal val mockModule = module {
    factory<SearchUserUseCase> { SearchUserUseCase.Empty }
}
