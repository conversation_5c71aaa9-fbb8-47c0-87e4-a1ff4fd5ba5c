package com.notemess.feature.contact.presentation.addcontact

import com.notemess.feature.user.User
import com.notemess.feature.user.UserSearchType

internal data class AddContactUiState(
    val searchQueryField: String,
    val searchTypes: List<UserSearchType>,
    val selectedSearchType: UserSearchType,
    val isLoading: <PERSON><PERSON><PERSON>,
    val isSearchButtonEnabled: <PERSON><PERSON><PERSON>,
    val foundUser: User?,
    val error: Throwable?
) {

    companion object {
        val Default = AddContactUiState(
            searchQueryField = "",
            searchTypes = UserSearchType.entries,
            selectedSearchType = UserSearchType.Email,
            isLoading = false,
            isSearchButtonEnabled = false,
            foundUser = null,
            error = null
        )
    }
}