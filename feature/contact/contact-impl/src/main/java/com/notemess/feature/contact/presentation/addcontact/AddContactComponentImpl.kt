package com.notemess.feature.contact.presentation.addcontact

import com.ark<PERSON>nov.decompose.ComponentContext
import com.domain.AuthCredentialsValidator
import com.notemess.core.ui.decompose.componentScope
import com.notemess.core.ui.preview.previewDataOrDefault
import com.notemess.feature.user.UserSearchQuery
import com.notemess.feature.user.UserSearchType
import com.notemess.feature.user.usecase.SearchUserUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class AddContactComponentImpl(
    context: ComponentContext,
    private val searchUserUseCase: SearchUserUseCase,
) : ComponentContext by context,
    AddContactComponent {

    private val mutableState = MutableStateFlow(context.previewDataOrDefault(AddContactUiState.Default))
    override val state = mutableState.asStateFlow()

    override fun onQueryChanged(query: String) {
        mutableState.update { currentState ->
            currentState.copy(
                searchQueryField = query,
                isSearchButtonEnabled = isSearchQueryValid(query, currentState.selectedSearchType),
                foundUser = null, // Сбрасываем найденного пользователя при изменении запроса
                error = null
            )
        }
    }

    override fun onSearchTypeChanged(searchType: UserSearchType) {
        mutableState.update { currentState ->
            currentState.copy(
                selectedSearchType = searchType,
                isSearchButtonEnabled = isSearchQueryValid(currentState.searchQueryField, searchType),
                foundUser = null, // Сбрасываем найденного пользователя при изменении типа
                error = null
            )
        }
    }

    override fun onKeyboardDone() {
        search()
    }

    override fun onSearchClicked() {
        search()
    }

    override fun onAddContactClicked() {
        val foundUser = state.value.foundUser ?: return

        // TODO: Implement add contact logic
        // Здесь должна быть логика добавления контакта
        // Например, вызов AddContactUseCase
    }

    private fun search() {
        val currentState = state.value
        if (!currentState.isSearchButtonEnabled || currentState.isLoading) {
            return
        }

        val query = UserSearchQuery(
            type = currentState.selectedSearchType,
            query = currentState.searchQueryField.trim()
        )

        componentScope.launch {
            mutableState.update { it.copy(isLoading = true, error = null) }

            val result = searchUserUseCase(query)
            result.fold(
                onSuccess = { user ->
                    mutableState.update { currentState ->
                        currentState.copy(
                            isLoading = false,
                            foundUser = user,
                            error = null
                        )
                    }
                },
                onFailure = { error ->
                    mutableState.update { currentState ->
                        currentState.copy(
                            isLoading = false,
                            foundUser = null,
                            error = error
                        )
                    }
                }
            )
        }
    }

    private fun isSearchQueryValid(query: String, searchType: UserSearchType): Boolean {
        val trimmedQuery = query.trim()

        if (trimmedQuery.isBlank()) {
            return false
        }

        return when (searchType) {
            UserSearchType.Email -> AuthCredentialsValidator.isEmailValid(trimmedQuery)
            UserSearchType.Name -> trimmedQuery.length >= MIN_NAME_LENGTH
        }
    }

    companion object {
        private const val MIN_NAME_LENGTH = 2
    }
}