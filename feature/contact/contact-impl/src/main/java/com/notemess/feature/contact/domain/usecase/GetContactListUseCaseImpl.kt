package com.notemess.feature.contact.domain.usecase

import com.notemess.feature.account.AccountRepository
import com.notemess.feature.contact.domain.ContactRepository
import com.notemess.feature.contact.domain.GetContactListUseCase
import com.notemess.feature.contact.model.Contact
import com.notemess.feature.user.UserId
import com.notemess.feature.user.usecase.GetUserByIdUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.flowOf
import kotlin.collections.map

internal class GetContactListUseCaseImpl(
    private val accountRepository: AccountRepository,
    private val contactRepository: ContactRepository,
    private val getUserByIdUseCase: GetUserByIdUseCase
) : GetContactListUseCase {

    override fun invoke(): Flow<Result<List<Contact.Full>>> {
        return currentAccountId()
            .flatMapLatest { currentAccountId ->
                contactRepository.getContactList(accountId = currentAccountId).localFirstAndThenRemote()
            }
            .flatMapLatest { contactListResult ->
                contactListResult.fold(
                    onSuccess = { contactListData ->
                        val contactList = contactListData.value

                        if (contactList.isEmpty()) {
                            return@fold flowOf(Result.success(emptyList()))
                        }

                        val fullContactFlows = contactList.map { contact ->
                            if (contact is Contact.Full) {
                                flowOf(Result.success(contact))
                            } else {
                                getUserByIdUseCase(contact.userId).map { userResult ->
                                    userResult.map { user -> contact.toFull(user) }
                                }
                            }
                        }

                        combine(fullContactFlows) { results ->
                            runCatching {
                                results.map { it.getOrThrow() }
                            }
                        }
                    },
                    onFailure = { error ->
                        flowOf(Result.failure(error))
                    }
                )
            }
    }

    private fun currentAccountId(): Flow<UserId> {
        return flow {
            val account = accountRepository.getAccount() ?: return@flow

            emit(UserId(account.id))
        }
    }
}
