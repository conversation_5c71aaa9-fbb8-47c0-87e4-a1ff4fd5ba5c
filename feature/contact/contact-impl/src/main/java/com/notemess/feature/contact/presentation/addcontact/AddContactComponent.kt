package com.notemess.feature.contact.presentation.addcontact

import com.notemess.core.feature.FeatureComponent
import com.notemess.feature.user.UserSearchType
import kotlinx.coroutines.flow.StateFlow

internal interface AddContactComponent: FeatureComponent {

    val state: StateFlow<AddContactUiState>

    fun onQueryChanged(query: String)

    fun onSearchTypeChanged(searchType: UserSearchType)

    fun onKeyboardDone()

    fun onSearchClicked()

    fun onAddContactClicked()
}