package com.notemess.feature.user.firebase

import com.google.firebase.firestore.FieldPath
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.toObject
import com.notemess.feature.user.UserId
import kotlinx.coroutines.tasks.await

class FirebaseUserCollection(
    firestore: FirebaseFirestore
) {

    private val usersCollection = firestore.collection("users")

    suspend fun getUserById(userId: UserId): Result<FirebaseUserEntity> {
        return runCatching {
            val documentRef = usersCollection.document(userId.value)
            val document = documentRef.get().await() ?: error("User not found")

            document.toObject<FirebaseUserEntity>() ?: error("Wrong user format")
        }
    }

    suspend fun getUserListByIds(userIds: List<UserId>): Result<List<FirebaseUserEntity>> {
        if (userIds.isEmpty()) {
            return Result.success(emptyList())
        }

        return runCatching {
            val userIdList = userIds.map { it.value }
            val documentList = usersCollection.whereIn(FieldPath.documentId(), userIdList).get().await()

            documentList.mapNotNull { document -> document.toObject<FirebaseUserEntity>() }
        }
    }
}
