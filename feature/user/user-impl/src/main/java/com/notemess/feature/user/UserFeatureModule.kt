package com.notemess.feature.user

import com.notemess.feature.user.data.UserRepositoryImpl
import com.notemess.feature.user.data.source.LocalUserDataSource
import com.notemess.feature.user.data.source.RemoteUserDataSource
import com.notemess.feature.user.data.source.db.DatabaseUserDataSource
import com.notemess.feature.user.data.source.firebase.FirebaseUserDataSource
import com.notemess.feature.user.domain.GetUserByIdUseCaseImpl
import com.notemess.feature.user.domain.SearchUserUseCaseImpl
import com.notemess.feature.user.usecase.GetUserByIdUseCase
import com.notemess.feature.user.usecase.SearchUserUseCase
import org.koin.core.module.Module
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

public val userFeatureModule: Module = module {
    singleOf(::FirebaseUserDataSource).bind<RemoteUserDataSource>()
    singleOf(::DatabaseUserDataSource).bind<LocalUserDataSource>()
    singleOf(::UserRepositoryImpl).bind<UserRepository>()

    singleOf(::GetUserByIdUseCaseImpl).bind<GetUserByIdUseCase>()
    singleOf(::SearchUserUseCaseImpl).bind<SearchUserUseCase>()
}
