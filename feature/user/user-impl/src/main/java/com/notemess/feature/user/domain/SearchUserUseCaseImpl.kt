package com.notemess.feature.user.domain

import com.notemess.feature.user.UserSearchQuery
import com.notemess.feature.user.User
import com.notemess.feature.user.UserRepository
import com.notemess.feature.user.usecase.SearchUserUseCase

internal class SearchUserUseCaseImpl(
    private val userRepository: UserRepository
) : SearchUserUseCase {

    override suspend fun invoke(query: UserSearchQuery): Result<User?> {
        TODO("Not yet implemented")
    }
}