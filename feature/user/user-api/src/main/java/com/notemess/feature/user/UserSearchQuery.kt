package com.notemess.feature.user

sealed interface UserSearchQuery {

    val type: UserSearchType
    val query: String

    data class Email(override val query: String) : UserSearchQuery {
        override val type = UserSearchType.Email
    }

    data class Name(override val query: String) : UserSearchQuery {
        override val type = UserSearchType.Name
    }

    companion object {
        operator fun invoke(type: UserSearchType, query: String): UserSearchQuery {
            return when (type) {
                UserSearchType.Email -> Email(query)
                UserSearchType.Name -> Name(query)
            }
        }
    }
}