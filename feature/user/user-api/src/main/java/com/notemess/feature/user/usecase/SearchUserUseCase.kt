package com.notemess.feature.user.usecase

import com.notemess.feature.user.UserSearchQuery
import com.notemess.feature.user.User

interface SearchUserUseCase {

    suspend operator fun invoke(query: UserSearchQuery): Result<User?>

    companion object {
        val Empty = object : SearchUserUseCase {
            override suspend fun invoke(query: UserSearchQuery): Result<User?> {
                return Result.success(null)
            }
        }
    }
}