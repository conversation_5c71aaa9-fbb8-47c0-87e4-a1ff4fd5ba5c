package com.notemess.feature.task.member.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMemberRole
import com.notemess.feature.task.member.db.entity.DatabaseTaskMemberEntity
import com.notemess.feature.task.member.db.entity.DatabaseTaskMemberWithUser
import com.notemess.feature.user.UserId
import kotlinx.coroutines.flow.Flow

@Dao
interface DatabaseTaskMemberDao {

    @Query("SELECT * FROM task_members_with_user WHERE memberId = :taskMemberId")
    fun observeTaskMember(taskMemberId: String): Flow<DatabaseTaskMemberWithUser?>

    @Query("SELECT * FROM task_members_with_user WHERE taskId = :taskId")
    fun observeTaskMemberListByTaskId(taskId: TaskId): Flow<List<DatabaseTaskMemberWithUser>>

    @Query("SELECT * FROM task_members_with_user WHERE taskId = :taskId AND memberUserId = :userId")
    suspend fun getTaskMember(taskId: TaskId, userId: UserId): DatabaseTaskMemberWithUser?

    @Query("SELECT * FROM task_members_with_user WHERE taskId = :taskId")
    suspend fun getTaskMemberListByTaskId(taskId: TaskId): List<DatabaseTaskMemberWithUser>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceTaskMember(taskMember: DatabaseTaskMemberEntity)

    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceTaskMembers(taskMembers: List<DatabaseTaskMemberEntity>)

    @Query("UPDATE task_members SET role = :role WHERE taskId = :taskId AND memberUserId = :userId")
    suspend fun updateTaskMemberRole(taskId: TaskId, userId: UserId, role: TaskMemberRole)

    @Query("DELETE FROM task_members WHERE taskId = :taskId AND memberUserId = :userId")
    suspend fun deleteTaskMember(taskId: TaskId, userId: UserId)
}
