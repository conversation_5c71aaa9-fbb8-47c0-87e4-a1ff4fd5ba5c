package com.notemess.feature.task.member.usecase

import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.user.UserId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow

interface GetFullTaskMemberUseCase {
    operator fun invoke(taskId: TaskId, userId: UserId): Flow<Result<TaskMember.Full>>

    companion object {
        val Empty = object : GetFullTaskMemberUseCase {
            override fun invoke(taskId: TaskId, userId: UserId): Flow<Result<TaskMember.Full>> {
                return emptyFlow()
            }
        }
    }
}
