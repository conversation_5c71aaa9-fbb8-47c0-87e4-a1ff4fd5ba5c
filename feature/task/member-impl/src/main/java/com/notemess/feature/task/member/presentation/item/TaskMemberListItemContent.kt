package com.notemess.feature.task.member.presentation.item

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.notemess.core.ui.foundation.AppTheme
import com.notemess.feature.user.UserId

@Composable
internal fun TaskMemberListItemContent(
    modifier: Modifier = Modifier,
    item: TaskMemberListItem,
    onClick: () -> Unit
) {
    Surface(
        modifier = modifier,
        onClick = onClick
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                modifier = Modifier.height(52.dp),
                checked = item.isSelected,
                onCheckedChange = { onClick() }
            )

            Text(
                text = item.title,
                color = AppTheme.colorScheme.textPrimary,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@PreviewLightDark
@Composable
private fun TaskMemberListItemContentPreview(
    @PreviewParameter(ItemsPreviewProvider::class)
    item: TaskMemberListItem
) = AppTheme {
    var itemState by remember { mutableStateOf(item) }

    TaskMemberListItemContent(
        modifier = Modifier.fillMaxWidth(),
        item = itemState,
        onClick = {
            itemState = itemState.copy(isSelected = itemState.isSelected.not())
        }
    )
}

private class ItemsPreviewProvider : PreviewParameterProvider<TaskMemberListItem> {
    override val values = sequenceOf(
        TaskMemberListItem(
            userId = UserId("1"),
            title = "Denis"
        )
    )
}