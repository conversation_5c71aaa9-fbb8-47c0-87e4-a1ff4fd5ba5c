package com.notemess.feature.task.member

import com.notemess.core.feature.FeatureEntryPoint
import com.notemess.feature.contact.domain.GetContactListUseCase
import com.notemess.feature.task.member.data.TaskMemberRepositoryImpl
import com.notemess.feature.task.member.data.source.db.DatabaseTaskMemberDataSource
import com.notemess.feature.task.member.data.source.LocalTaskMemberDataSource
import com.notemess.feature.task.member.data.source.RemoteTaskMemberDataSource
import com.notemess.feature.task.member.data.source.TaskMemberIdFactory
import com.notemess.feature.task.member.data.source.firebase.FirebaseTaskMemberDataSource
import com.notemess.feature.task.member.data.source.firebase.FirebaseTaskMemberIdFactory
import com.notemess.feature.task.member.domain.usecase.GetFullTaskMemberUseCaseImpl
import com.notemess.feature.task.member.domain.usecase.GetFullTaskMembersByTaskIdUseCaseImpl
import com.notemess.feature.task.member.domain.TaskMemberRepository
import com.notemess.feature.task.member.domain.edit.TaskMemberEditManager
import com.notemess.feature.task.member.domain.edit.TaskMemberEditManagerImpl
import com.notemess.feature.task.member.domain.usecase.GetTaskMembersEditSessionUseCase
import com.notemess.feature.task.member.presentation.TaskMemberListComponent
import com.notemess.feature.task.member.presentation.TaskMemberListComponentImpl
import com.notemess.feature.task.member.usecase.GetFullTaskMemberUseCase
import com.notemess.feature.task.member.usecase.GetFullTaskMembersByTaskIdUseCase
import org.koin.core.module.Module
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.onOptions
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module
import javax.inject.Provider

public val taskMemberFeatureModule: Module = module {
    factoryOf(::TaskMemberListFeatureEntryPoint).onOptions { bind<FeatureEntryPoint<*>>() }

    singleOf(::FirebaseTaskMemberIdFactory).bind<TaskMemberIdFactory>()
    singleOf(::FirebaseTaskMemberDataSource).bind<RemoteTaskMemberDataSource>()
    singleOf(::DatabaseTaskMemberDataSource).bind<LocalTaskMemberDataSource>()
    singleOf(::TaskMemberRepositoryImpl).bind<TaskMemberRepository>()
    singleOf(::TaskMemberEditManagerImpl).bind<TaskMemberEditManager>()

    factoryOf(::GetTaskMembersEditSessionUseCase)
    factory<Provider<GetTaskMembersEditSessionUseCase>> { Provider<GetTaskMembersEditSessionUseCase> { get() } }
    factoryOf(::GetFullTaskMemberUseCaseImpl).bind<GetFullTaskMemberUseCase>()
    factoryOf(::GetFullTaskMembersByTaskIdUseCaseImpl).bind<GetFullTaskMembersByTaskIdUseCase>()

    factoryOf(::TaskMemberListComponentImpl).bind<TaskMemberListComponent>()
}

internal val mockModule = module {
    factory<GetContactListUseCase> { GetContactListUseCase.Empty }
    factory<GetFullTaskMemberUseCase> { GetFullTaskMemberUseCase.Empty }
}
