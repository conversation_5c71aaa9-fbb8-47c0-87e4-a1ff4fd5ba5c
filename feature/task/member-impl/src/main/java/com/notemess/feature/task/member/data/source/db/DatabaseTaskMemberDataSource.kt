package com.notemess.feature.task.member.data.source.db

import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.data.source.LocalTaskMemberDataSource
import com.notemess.feature.task.member.db.DatabaseTaskMemberDao
import com.notemess.feature.task.member.db.DatabaseTaskMemberMapper
import com.notemess.feature.task.member.db.entity.DatabaseTaskMemberEntity
import com.notemess.feature.task.member.domain.TaskMemberMutation
import com.notemess.feature.user.UserId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map

internal class DatabaseTaskMemberDataSource(
    private val databaseTaskMemberDao: DatabaseTaskMemberDao,
) : LocalTaskMemberDataSource {

    override suspend fun getTaskMember(taskId: TaskId, userId: UserId): Result<TaskMember> {
        return runCatching {
            val taskMemberEntity = databaseTaskMemberDao.getTaskMember(taskId, userId)
            taskMemberEntity?.let(DatabaseTaskMemberMapper::toDomain) ?: error("Task member not found")
        }
    }

    override suspend fun getTaskMemberListByTaskId(taskId: TaskId): Result<List<TaskMember>> {
        return runCatching {
            val taskMemberEntityList = databaseTaskMemberDao.getTaskMemberListByTaskId(taskId)
            taskMemberEntityList.map(DatabaseTaskMemberMapper::toDomain)
        }
    }

    override fun observeTaskMember(taskMemberId: String): Flow<TaskMember> {
        return databaseTaskMemberDao.observeTaskMember(taskMemberId)
            .filterNotNull()
            .map(DatabaseTaskMemberMapper::toDomain)
    }

    override fun observeTaskMemberListByTaskId(taskId: TaskId): Flow<List<TaskMember>> {
        return databaseTaskMemberDao.observeTaskMemberListByTaskId(taskId)
            .filterNotNull()
            .map { taskMemberEntityList -> taskMemberEntityList.map(DatabaseTaskMemberMapper::toDomain) }
    }

    override suspend fun insertOrReplaceTaskMembers(taskMembers: List<TaskMember>): Result<Unit> {
        return runCatching {
            val taskMemberEntities = taskMembers.map(DatabaseTaskMemberMapper::toDatabaseEntity)
            databaseTaskMemberDao.insertOrReplaceTaskMembers(taskMemberEntities)
        }
    }

    override suspend fun applyMutationTaskMember(mutation: TaskMemberMutation): Result<Unit> {
        return runCatching {
            when (mutation) {
                is TaskMemberMutation.Add -> {
                    val taskMemberEntity = DatabaseTaskMemberEntity(
                        memberId = mutation.userId.value,
                        taskId = mutation.taskId,
                        type = mutation.type,
                        role = mutation.role,
                        memberUserId = mutation.userId,
                    )
                    databaseTaskMemberDao.insertOrReplaceTaskMember(taskMemberEntity)
                }

                is TaskMemberMutation.Update -> {
                    databaseTaskMemberDao.updateTaskMemberRole(mutation.taskId, mutation.userId, mutation.role)
                }

                is TaskMemberMutation.Remove -> {
                    databaseTaskMemberDao.deleteTaskMember(mutation.taskId, mutation.userId)
                }
            }
        }
    }

}