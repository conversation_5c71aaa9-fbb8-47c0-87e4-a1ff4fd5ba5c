package com.notemess.feature.task.member.domain

import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMemberRole
import com.notemess.feature.task.member.TaskMemberType
import com.notemess.feature.user.UserId

internal sealed interface TaskMemberMutation {

    val taskId: TaskId
    val userId: UserId

    data class Update(
        override val taskId: TaskId,
        override val userId: UserId,
        val role: TaskMemberRole
    ) : TaskMemberMutation

    data class Add(
        override val taskId: TaskId,
        override val userId: UserId,
        val type: TaskMemberType,
        val role: TaskMemberRole
    ) : TaskMemberMutation

    data class Remove(
        override val taskId: TaskId,
        override val userId: UserId
    ) : TaskMemberMutation
}