package com.notemess.feature.task.member.presentation

import com.arkivanov.decompose.ComponentContext
import com.notemess.core.feature.FeatureComponentFactory
import com.notemess.core.ui.decompose.DecomposeFeatureDependencies
import com.notemess.core.ui.decompose.DecomposeHost
import com.notemess.core.ui.decompose.componentScope
import com.notemess.core.ui.decompose.host
import com.notemess.core.ui.feature.router.FeatureNavigationChild
import com.notemess.core.ui.preview.previewDataOrDefault
import com.notemess.core.ui.preview.previewDataOrNull
import com.notemess.feature.contact.AddContactScreen
import com.notemess.feature.contact.domain.GetContactListUseCase
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.TaskMemberListScreenFeature
import com.notemess.feature.task.member.presentation.item.TaskMemberListItem
import com.notemess.feature.task.member.domain.usecase.GetTaskMembersEditSessionUseCase
import com.notemess.feature.task.member.usecase.GetFullTaskMemberUseCase
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Provider

internal class TaskMemberListComponentImpl(
    context: ComponentContext,
    params: TaskMemberListScreenFeature.Params,
    getTaskMembersEditSessionUseCaseProvider: Provider<GetTaskMembersEditSessionUseCase>,
    private val featureComponentFactory: FeatureComponentFactory,
    private val getContactListUseCase: GetContactListUseCase,
    private val getFullTaskMemberUseCase: GetFullTaskMemberUseCase,
) : ComponentContext by context,
    TaskMemberListComponent {

    private val host = host<Configuration, TaskMemberListComponent.Child> {
        registerChildFactory(AddContactChildFactory(featureComponentFactory))
    }

    private val mutableState = MutableStateFlow(context.previewDataOrDefault(TaskMemberListState.Default))
    override val state = mutableState.asStateFlow()
    override val childStack = host.childStack

    private val editSession by lazy { getTaskMembersEditSessionUseCaseProvider.get().invoke(params.taskId) }

    init {
        if (context.previewDataOrNull<TaskMemberListState>() == null) {
            componentScope.launch { observeTaskMemberList() }
        }
    }

    override fun onItemClicked(item: TaskMemberListItem) {
        componentScope.launch {
            if (item.isSelected) {
                editSession.removeMemberByUserId(item.userId)
            } else {
                editSession.addMemberByUserId(item.userId)
            }
        }
    }

    override fun onAddContactClicked() {
        TODO("Not yet implemented")
    }

    private suspend fun observeTaskMemberList() {
        combine(
            getContactListUseCase(),
            editSessionFullTaskMemberList()
        ) { contactListResult, taskMemberListResult ->
            contactListResult to taskMemberListResult
        }.collect { (contactListResult, taskMemberListResult) ->
            val memberListResult = contactListResult.mapCatching { contactList ->
                TaskMemberListUiMapper.toUIItems(contactList, taskMemberListResult.getOrThrow())
            }

            memberListResult.fold(
                onSuccess = { taskMemberList ->
                    mutableState.update { state ->
                        TaskMemberListState.Content(
                            items = taskMemberList.toPersistentList()
                        )
                    }
                },
                onFailure = { error ->
                    mutableState.update { state ->
                        TaskMemberListState.Error(error)
                    }
                }
            )
        }
    }

    private fun editSessionFullTaskMemberList(): Flow<Result<List<TaskMember.Full>>> {
        return editSession.taskMemberList().flatMapMerge { taskMemberResult ->
            taskMemberResult.fold(
                onSuccess = { taskMemberList ->
                    val fullMemberFlowList = taskMemberList.map { taskMember ->
                        if (taskMember is TaskMember.Full) {
                            flowOf(Result.success(taskMember))
                        } else {
                            getFullTaskMemberUseCase(taskMember.taskId, taskMember.userId)
                        }
                    }

                    combine(fullMemberFlowList) { fullMemberResultList ->
                        runCatching {
                            fullMemberResultList.map { fullMemberResult ->
                                fullMemberResult.getOrThrow()
                            }
                        }
                    }
                },
                onFailure = { error -> flowOf(Result.failure(error)) }
            )
        }
    }

    private class AddContactChildFactory(
        private val featureComponentFactory: FeatureComponentFactory
    ) :
        DecomposeHost.ChildFactoryDelegate<Configuration.AddContact, TaskMemberListComponent.Child.ExternalFeature> {
        override fun createChild(
            configuration: Configuration.AddContact,
            context: ComponentContext
        ): TaskMemberListComponent.Child.ExternalFeature {
            return TaskMemberListComponent.Child.ExternalFeature(
                feature = FeatureNavigationChild(
                    featureId = AddContactScreen.id,
                    featureComponent = featureComponentFactory.createComponent(
                        feature = AddContactScreen,
                        dependencies = DecomposeFeatureDependencies(context),
                        callbacks = null
                    )
                ),
            )
        }
    }

    sealed interface Configuration {
        data object TaskMemberList : Configuration
        data object AddContact : Configuration
    }
}
