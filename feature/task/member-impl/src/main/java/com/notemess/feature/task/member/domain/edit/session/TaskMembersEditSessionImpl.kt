package com.notemess.feature.task.member.domain.edit.session

import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.TaskMemberRole
import com.notemess.feature.task.member.TaskMemberType
import com.notemess.feature.task.member.data.source.TaskMemberIdFactory
import com.notemess.feature.task.member.domain.TaskMemberMutation
import com.notemess.feature.task.member.domain.TaskMemberRepository
import com.notemess.feature.user.UserId
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlin.collections.plus

internal class TaskMembersEditSessionImpl(
    private val taskId: TaskId,
    private val taskMemberRepository: TaskMemberRepository,
    private val taskMemberIdFactory: TaskMemberIdFactory,
) : TaskMembersEditSession {

    private val mutations = MutableStateFlow(emptyMap<UserId, TaskMemberMutation>())

    override fun taskMemberList(): Flow<Result<List<TaskMember>>> {
        return combine(
            taskMemberRepository.getTaskMemberListByTaskId(taskId).localFirstAndThenRemote(),
            mutations
        ) { taskMemberResult, mutations ->
            taskMemberResult.map { taskMemberListData -> applyMutationTo(taskMemberListData.value, mutations) }
        }
    }

    override suspend fun addMemberByUserId(userId: UserId): Result<Unit> {
        return runCatching {
            val taskMember = taskMemberRepository.getTaskMember(taskId, userId).load().getOrNull()

            if (taskMember != null) {
                updateMutation { remove(userId) }
            } else {
                updateMutation { set(userId, TaskMemberMutation.Add(taskId, userId, DefaultTaskType, DefaultTaskRole)) }
            }
        }
    }

    override suspend fun removeMemberByUserId(userId: UserId): Result<Unit> {
        return runCatching {
            val taskMember = taskMemberRepository.getTaskMember(taskId, userId).load().getOrNull()

            if (taskMember != null) {
                updateMutation { set(userId, TaskMemberMutation.Remove(taskId, userId)) }
            } else {
                updateMutation { remove(userId) }
            }
        }
    }

    override suspend fun updateMemberRoleByUserId(userId: UserId, role: TaskMemberRole): Result<Unit> {
        return runCatching {
            val taskMember = taskMemberRepository.getTaskMember(taskId, userId).load().getOrNull()

            if (taskMember != null) {
                updateMutation { set(userId, TaskMemberMutation.Update(taskId, userId, role)) }
            } else {
                updateMutation { set(userId, TaskMemberMutation.Add(taskId, userId, DefaultTaskType, role)) }
            }
        }
    }

    override suspend fun commit(): Result<Unit> {
        return coroutineScope {
            val mutationResults = mutations.value.values.map { mutation ->
                async { taskMemberRepository.applyMutation(mutation) }
            }.awaitAll()

            mutationResults.forEach { result ->
                result.onFailure { error -> return@coroutineScope Result.failure(error) }
            }

            Result.success(Unit)
        }
    }

    private fun TaskMemberMutation.toTaskMemberOrNull(): TaskMember? {
        return when (this) {
            is TaskMemberMutation.Add,
            is TaskMemberMutation.Update -> TaskMember.Short(
                id = taskMemberIdFactory.create(userId),
                type = TaskMemberType.Inheritor,
                role = TaskMemberRole.Member,
                taskId = taskId,
                userId = userId
            )

            is TaskMemberMutation.Remove -> null
        }
    }

    private fun updateMutation(block: MutableMap<UserId, TaskMemberMutation>.() -> Unit) {
        mutations.value = mutations.value.toMutableMap().apply(block).toMap()
    }

    private fun applyMutationTo(
        taskMembers: List<TaskMember>,
        mutations: Map<UserId, TaskMemberMutation>
    ): List<TaskMember> {
        val mutationMembers = mutations.values.mapNotNull { mutation -> mutation.toTaskMemberOrNull() }

        return (taskMembers + mutationMembers)
            .filterNot { taskMember -> mutations[taskMember.userId] is TaskMemberMutation.Remove }
    }

    companion object {
        private val DefaultTaskType = TaskMemberType.Inheritor
        private val DefaultTaskRole = TaskMemberRole.Member
    }
}