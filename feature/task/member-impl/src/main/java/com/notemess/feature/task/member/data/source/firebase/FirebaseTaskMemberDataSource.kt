package com.notemess.feature.task.member.data.source.firebase

import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.toObject
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.TaskMemberType
import com.notemess.feature.task.member.data.source.RemoteTaskMemberDataSource
import com.notemess.feature.task.member.domain.TaskMemberMutation
import com.notemess.feature.user.UserId
import kotlinx.coroutines.tasks.await

internal class FirebaseTaskMemberDataSource(
    firestore: FirebaseFirestore,
) : RemoteTaskMemberDataSource {

    private val tasksCollection = firestore.collection("tasks_v2")

    override suspend fun getTaskMember(
        taskId: TaskId,
        userId: UserId
    ): Result<TaskMember> {
        return runCatching {
            val document = tasksCollection.document(taskId.value).get().await()
            val taskEntity = document.toObject<FirebaseTaskEntity>() ?: error("Task not found")

            FirebaseTaskMemberMapper.toDomain(taskEntity, userId.value)
        }
    }

    override suspend fun getTaskMemberListByTaskId(taskId: TaskId): Result<List<TaskMember>> {
        return runCatching {
            val document = tasksCollection.document(taskId.value).get().await()
            val taskEntity = document.toObject<FirebaseTaskEntity>() ?: error("Task not found")

            FirebaseTaskMemberMapper.toDomain(taskEntity)
        }
    }

    override suspend fun applyMutationTaskMember(mutation: TaskMemberMutation): Result<Unit> {
        val updates = buildMap<String, Any> {
            when (mutation) {
                is TaskMemberMutation.Remove -> {
                    put("memberRoles.${mutation.userId.value}", FieldValue.delete())
                    put("initialMemberIds", FieldValue.arrayRemove(mutation.userId.value))
                    put("inheritMemberIds", FieldValue.arrayRemove(mutation.userId.value))
                }

                is TaskMemberMutation.Add -> {
                    val roleFieldValue = FirebaseTaskMemberMapper.toFirebase(mutation.role)
                    put("memberRoles.${mutation.userId.value}", roleFieldValue)

                    val idFieldValue = FieldValue.arrayUnion(mutation.userId.value)
                    when (mutation.type) {
                        TaskMemberType.Initiator -> put("initialMemberIds", idFieldValue)
                        TaskMemberType.Inheritor -> put("inheritMemberIds", idFieldValue)
                    }
                }

                is TaskMemberMutation.Update -> {
                    val roleFieldValue = FirebaseTaskMemberMapper.toFirebase(mutation.role)
                    put("memberRoles.${mutation.userId.value}", roleFieldValue)
                }
            }
        }

        return runCatching {
            tasksCollection.document(mutation.taskId.value).update(updates)
        }
    }
}