package com.notemess.feature.task.member.data.source.firebase

import com.google.firebase.firestore.DocumentId

internal data class FirebaseTaskEntity(
    @DocumentId
    val id: String = "",
    val initialMemberIds: List<String> = emptyList(),
    val inheritMemberIds: List<String> = emptyList(),
    val memberRoles: Map<String, Member> = emptyMap(),
) {

    data class Member(
        val role: String = "",
    )
}