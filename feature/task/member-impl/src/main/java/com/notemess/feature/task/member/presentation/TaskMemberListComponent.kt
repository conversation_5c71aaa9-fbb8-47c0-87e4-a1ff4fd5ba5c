package com.notemess.feature.task.member.presentation

import com.notemess.core.feature.FeatureComponent
import com.notemess.feature.task.member.presentation.item.TaskMemberListItem
import kotlinx.coroutines.flow.StateFlow

internal interface TaskMemberListComponent : FeatureComponent {

    val state: StateFlow<TaskMemberListState>

    fun onItemClicked(item: TaskMemberListItem)

    fun onAddContactClicked()
}
