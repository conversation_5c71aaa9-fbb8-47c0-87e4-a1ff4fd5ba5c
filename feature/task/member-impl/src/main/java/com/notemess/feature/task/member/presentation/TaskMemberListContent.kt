package com.notemess.feature.task.member.presentation

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.notemess.core.ui.feature.ComposeFeatureContent
import com.notemess.core.ui.preview.PreviewSandbox
import com.notemess.core.ui.preview.injectPreviewComponent
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMemberListScreenFeature
import com.notemess.feature.task.member.mockModule
import com.notemess.feature.task.member.presentation.item.TaskMemberListItem
import com.notemess.feature.task.member.presentation.item.TaskMemberListItemAddContact
import com.notemess.feature.task.member.presentation.item.TaskMemberListItemContent
import com.notemess.feature.task.member.taskMemberFeatureModule
import com.notemess.feature.user.UserId
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import org.koin.core.parameter.parametersOf

internal class TaskMemberListContent(
    private val component: TaskMemberListComponent
) : ComposeFeatureContent {

    @Composable
    override fun Composable(modifier: Modifier) {
        TaskMemberListContent(
            modifier = modifier,
            component = component
        )
    }
}

@Composable
private fun TaskMemberListContent(
    modifier: Modifier = Modifier,
    component: TaskMemberListComponent
) {
    val state by component.state.collectAsStateWithLifecycle()

    AnimatedContent(
        modifier = modifier,
        targetState = state,
        contentKey = { state -> state.javaClass.name }
    ) { state ->
        when (state) {
            is TaskMemberListState.Content -> {
                ListContent(
                    items = state.items,
                    onItemClicked = component::onItemClicked,
                    onAddContactClicked = component::onAddContactClicked
                )
            }

            is TaskMemberListState.Error -> {
                ErrorContent(error = state.throwable)
            }

            TaskMemberListState.Loading -> {
                LoadingContent()
            }
        }
    }
}

@Composable
private fun ListContent(
    modifier: Modifier = Modifier,
    items: ImmutableList<TaskMemberListItem>,
    onItemClicked: (TaskMemberListItem) -> Unit,
    onAddContactClicked: () -> Unit
) {
    Column(
        modifier
    ) {
        LazyColumn {
            item {
                TaskMemberListItemAddContact(
                    onClick = onAddContactClicked
                )
            }

            items(
                items = items,
                key = { item -> item.userId.value }
            ) { item ->
                TaskMemberListItemContent(
                    modifier = Modifier.fillMaxWidth(),
                    item = item,
                    onClick = { onItemClicked(item) }
                )
            }
        }
    }
}

@Composable
private fun LoadingContent(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun ErrorContent(
    modifier: Modifier = Modifier,
    error: Throwable
) {

}

@PreviewLightDark
@Composable
private fun TaskMemberListContentPreview(
    @PreviewParameter(StatePreviewProvider::class)
    state: TaskMemberListState
) = PreviewSandbox(
    application = { modules(taskMemberFeatureModule, mockModule) }
) {
    TaskMemberListContent(
        modifier = Modifier.fillMaxSize(),
        component = injectPreviewComponent(state) {
            parametersOf(TaskMemberListScreenFeature.Params(TaskId("")))
        }
    )
}

private class StatePreviewProvider : PreviewParameterProvider<TaskMemberListState> {
    override val values = sequenceOf(
        TaskMemberListState.Content(
            items = persistentListOf(
                TaskMemberListItem(
                    userId = UserId("1"),
                    title = "Denis Tkachenko"
                ),
                TaskMemberListItem(
                    userId = UserId("2"),
                    title = "Aleksey Sklyar"
                )
            )
        ),
        TaskMemberListState.Loading
    )
}