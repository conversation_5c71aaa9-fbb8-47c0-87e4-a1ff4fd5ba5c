package com.notemess.feature.task.member.presentation

import com.notemess.feature.contact.model.Contact
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.presentation.item.TaskMemberListItem

internal object TaskMemberListUiMapper {

    fun toUIItems(
        contactList: List<Contact.Full>,
        taskMemberList: List<TaskMember.Full>
    ): List<TaskMemberListItem> {
        val selectedMemberListResult = taskMemberList.map { taskMember ->
            TaskMemberListItem(
                userId = taskMember.userId,
                title = taskMember.user.displayName,
                isSelected = true
            )
        }

        val notMemberContactList = contactList.filter { contact ->
            taskMemberList.none { taskMember -> taskMember.userId == contact.user.id }
        }

        val notSelectedMemberList = notMemberContactList.map { contact ->
            TaskMemberListItem(
                userId = contact.user.id,
                title = contact.user.displayName,
                isSelected = false
            )
        }

        return selectedMemberListResult + notSelectedMemberList
    }
}