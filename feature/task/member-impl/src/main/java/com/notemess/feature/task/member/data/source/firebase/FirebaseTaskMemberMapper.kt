package com.notemess.feature.task.member.data.source.firebase

import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.member.TaskMember
import com.notemess.feature.task.member.TaskMemberRole
import com.notemess.feature.task.member.TaskMemberType
import com.notemess.feature.user.UserId

internal object FirebaseTaskMemberMapper {

    fun toDomain(
        entity: FirebaseTaskEntity,
    ): List<TaskMember> {
        val memberIds = entity.initialMemberIds + entity.inheritMemberIds
        return memberIds.map { memberId -> toDomain(entity, memberId) }
    }

    fun toDomain(
        entity: FirebaseTaskEntity,
        memberId: String,
    ): TaskMember {
        val type = when {
            entity.initialMemberIds.contains(memberId) -> TaskMemberType.Initiator
            entity.inheritMemberIds.contains(memberId) -> TaskMemberType.Inheritor
            else -> error("Member not found")
        }

        val role = when (entity.memberRoles[memberId]?.role) {
            "owner" -> TaskMemberRole.Owner
            "member" -> TaskMemberRole.Member
            else -> TaskMemberRole.Unknown
        }

        return TaskMember.Short(
            id = memberId,
            type = type,
            role = role,
            taskId = TaskId(entity.id),
            userId = UserId(memberId)
        )
    }

    fun toFirebase(type: TaskMemberType): String {
        return when (type) {
            TaskMemberType.Initiator -> "initiator"
            TaskMemberType.Inheritor -> "inheritor"
        }
    }

    fun toFirebase(role: TaskMemberRole): String {
        return when (role) {
            TaskMemberRole.Owner -> "owner"
            TaskMemberRole.Member -> "member"
            TaskMemberRole.Unknown -> "unknown"
        }
    }
}