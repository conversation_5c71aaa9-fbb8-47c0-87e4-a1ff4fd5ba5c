@file:OptIn(InternalSerializationApi::class)

package com.notemess.core.ui.decompose

import com.notemess.core.feature.Feature
import com.notemess.core.feature.FeatureCallbacks
import com.notemess.core.feature.FeatureComponent
import com.notemess.core.feature.FeatureContent
import com.notemess.core.feature.FeatureDependencies
import com.notemess.core.feature.FeatureEntryPoint
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.serializer
import kotlin.reflect.KClass
import kotlin.reflect.safeCast

class DecomposeFeatureEntryPoint<P, F : Feature<P>, C : FeatureComponent>(
    override val id: String,
    private val featureClass: KClass<F>,
    private val componentClass: KClass<out C>,
    private val componentFactory: DecomposeComponentFactory,
    private val contentFactory: FeatureContent.Factory<C>
) : FeatureEntryPoint<F> {

    override fun featureClass(): KClass<F> {
        return featureClass
    }

    override fun featureSerializer(): KSerializer<F> {
        return featureClass.serializer()
    }

    override fun <Params> createComponent(
        dependencies: FeatureDependencies,
        params: Params?,
        callbacks: FeatureCallbacks?
    ): FeatureComponent {
        val decomposeFeatureDependencies = DecomposeFeatureDependencies::class.safeCast(dependencies)
            ?: error("Expected DecomposeFeatureDependencies, but got $dependencies")

        return componentFactory.createComponent(
            context = decomposeFeatureDependencies.context,
            componentClass = componentClass,
            params = params,
            callbacks = callbacks
        )
    }

    override fun createContent(component: FeatureComponent): FeatureContent {
        val decomposeComponent = componentClass.safeCast(component) ?: error(
            "Expected $componentClass, but got $component"
        )

        return contentFactory.create(decomposeComponent)
    }
}