package com.notemess.core.ui.foundation

import android.content.Context
import android.os.Build
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color

val LocalAppColorScheme = compositionLocalOf<AppColorScheme> { AppColorSchemeImpl.light() }

interface AppColorScheme {
    val isDark: Boolean
    val material: ColorScheme
    val successColor: Color
    val barBorder: Color
    val textPrimary: Color
    val textSecondary: Color
    val textTertiary: Color
    val icons: Color
    val iconSecondary: Color
    val iconNegative: Color
    val backgroundNegative: Color
    val accent: Color
    val selectedText: Color
    val unselectedText: Color
}

@Immutable
internal class AppColorSchemeImpl private constructor(
    override val isDark: Boolean,
    private val defaultMaterial: ColorScheme,
) : AppColorScheme {

    override val material: ColorScheme = defaultMaterial.copy(
        primary = Color(0xFF007AFF),
        primaryContainer = Color(0xFF007AFF),
        outlineVariant = if (isDark) {
            Color(0xFF414141)
        } else {
            Color(0xFFE7E7E7)
        },
        background = if (isDark) {
            Color(0xFF121212)
        } else {
            Color(0xFFF3F3F3)
        },
        surface = if (isDark) {
            Color.Black
        } else {
            Color.White
        },
        onSurfaceVariant = Color(0xFF8A8A8E)
    )

    override val successColor: Color = Color.Green
    override val barBorder: Color =
        if (isDark) {
            Color.White
        } else {
            Color.Black
        }

    override val textPrimary: Color =
        if (isDark) {
            Color.White
        } else {
            Color(0xFF111111)
        }

    override val textSecondary: Color =
        if (isDark) {
            Color(0xFF999999)
        } else {
            Color(0x993C3C43)
        }

    override val textTertiary: Color =
        if (isDark) {
            Color(0xFF666666)
        } else {
            Color(0xFFB5B5B5)
        }

    override val icons: Color =
        if (isDark) {
            Color(0xFFDCDCDC)
        } else {
            Color(0xFF3E3E3E)
        }

    override val iconSecondary: Color = Color(0xFF858589)

    override val iconNegative: Color = Color(0xFFFF3F42)

    override val backgroundNegative: Color =
        if (isDark) {
            Color(0xFF340909)
        } else {
            Color(0xFFFFF0F0)
        }

    override val accent: Color = Color(0xFFFF594B)

    override val selectedText: Color =
        if (isDark) {
            Color(0xFFE5E5E5)
        } else {
            Color(0xFF222222)
        }

    override val unselectedText: Color =
        if (isDark) {
            Color(0xFF646464)
        } else {
            Color(0xFF999999)
        }

    companion object {
        internal fun dark(context: Context? = null, isDynamic: Boolean = false) =
            AppColorSchemeImpl(
                isDark = true,
                defaultMaterial = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && isDynamic && context != null) {
                    dynamicDarkColorScheme(context)
                } else {
                    darkColorScheme()
                },
            )

        internal fun light(context: Context? = null, isDynamic: Boolean = false) =
            AppColorSchemeImpl(
                isDark = false,
                defaultMaterial = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && isDynamic && context != null) {
                    dynamicLightColorScheme(context)
                } else {
                    lightColorScheme()
                },
            )

        internal fun create(
            context: Context? = null,
            isDynamic: Boolean,
            isDark: Boolean
        ): AppColorScheme {
            return if (isDark) {
                dark(context, isDynamic)
            } else {
                light(context, isDynamic)
            }
        }
    }
}
