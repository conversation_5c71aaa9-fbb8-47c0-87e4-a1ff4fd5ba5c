package com.notemess.core.ui.decompose

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.value.Value

interface DecomposeHost<Configuration : Any, Child : Any> {

    val navigation: StackNavigation<Configuration>

    val childStack: Value<ChildStack<Configuration, Child>>

    interface ChildFactoryDelegate<Configuration : Any, Child : Any> {
        fun createChild(configuration: Configuration, context: ComponentContext): Child
    }
}