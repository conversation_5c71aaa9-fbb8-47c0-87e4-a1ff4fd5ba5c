{"formatVersion": 1, "database": {"version": 1, "identityHash": "bb5bfd053707432da3a2e98b06f86446", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `displayName` TEXT NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}}, {"tableName": "contacts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`contactId` TEXT NOT NULL, `accountId` TEXT NOT NULL, `contactUserId` TEXT NOT NULL, PRIMARY KEY(`contactId`))", "fields": [{"fieldPath": "contactId", "columnName": "contactId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "accountId", "columnName": "accountId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contactUserId", "columnName": "contactUserId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["contactId"]}}, {"tableName": "task_members", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`memberId` TEXT NOT NULL, `taskId` TEXT NOT NULL, `type` TEXT NOT NULL, `role` TEXT NOT NULL, `memberUserId` TEXT NOT NULL, PRIMARY KEY(`taskId`, `memberId`))", "fields": [{"fieldPath": "memberId", "columnName": "memberId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "taskId", "columnName": "taskId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": true}, {"fieldPath": "memberUserId", "columnName": "memberUserId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["taskId", "memberId"]}}, {"tableName": "tasks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`taskId` TEXT NOT NULL, `parentTaskId` TEXT, `title` TEXT NOT NULL, `type` TEXT NOT NULL, `description` TEXT, `isPendingDeletion` INTEGER NOT NULL, `isDraft` INTEGER NOT NULL, `isDraftFromTemplate` INTEGER NOT NULL, `taskTime_type` TEXT, `taskTime_dueTimestamp` INTEGER, `taskTime_dueTimeZoneId` TEXT, `taskTime_startTimestamp` INTEGER, `taskTime_startTimeZoneId` TEXT, `modified_description` TEXT, PRIMARY KEY(`taskId`))", "fields": [{"fieldPath": "taskId", "columnName": "taskId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "parentTaskId", "columnName": "parentTaskId", "affinity": "TEXT"}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT"}, {"fieldPath": "isPendingDeletion", "columnName": "isPendingDeletion", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDraft", "columnName": "isDraft", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDraftFromTemplate", "columnName": "isDraftFromTemplate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "taskTime.type", "columnName": "taskTime_type", "affinity": "TEXT"}, {"fieldPath": "taskTime.dueTimestamp", "columnName": "taskTime_dueTimestamp", "affinity": "INTEGER"}, {"fieldPath": "taskTime.dueTimeZoneId", "columnName": "taskTime_dueTimeZoneId", "affinity": "TEXT"}, {"fieldPath": "taskTime.startTimestamp", "columnName": "taskTime_startTimestamp", "affinity": "INTEGER"}, {"fieldPath": "taskTime.startTimeZoneId", "columnName": "taskTime_startTimeZoneId", "affinity": "TEXT"}, {"fieldPath": "modified.description", "columnName": "modified_description", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["taskId"]}}, {"tableName": "DatabaseTaskMemberCrossRef", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`taskId` TEXT NOT NULL, `memberId` TEXT NOT NULL, `memberUserId` TEXT NOT NULL, PRIMARY KEY(`taskId`, `memberId`))", "fields": [{"fieldPath": "taskId", "columnName": "taskId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "memberId", "columnName": "memberId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "memberUserId", "columnName": "memberUserId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["taskId", "memberId"]}}], "views": [{"viewName": "contacts_with_user", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT contacts.*, users.* FROM contacts contacts\n        LEFT JOIN users users ON contacts.contactUserId = users.userId"}, {"viewName": "task_members_with_user", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT task_members.*, users.* FROM task_members task_members\n        LEFT JOIN users users ON task_members.memberUserId = users.userId"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'bb5bfd053707432da3a2e98b06f86446')"]}}