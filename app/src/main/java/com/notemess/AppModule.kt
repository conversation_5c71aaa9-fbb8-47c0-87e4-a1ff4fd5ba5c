package com.notemess

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.firestore.firestoreSettings
import com.google.firebase.firestore.persistentCacheSettings
import com.google.firebase.messaging.FirebaseMessaging
import com.notemess.core.feature.FeatureComponentFactory
import com.notemess.core.ui.decompose.DecomposeComponentFactory
import com.notemess.core.ui.decompose.DecomposeFeatureComponentFactory
import com.notemess.core.ui.decompose.DecomposeFeatureSerializer
import com.notemess.core.ui.feature.ComposeFeatureContentRenderer
import com.notemess.core.ui.feature.ComposeFeatureContentRendererImpl
import com.notemess.core.ui.feature.router.DecomposeFeatureNavigationRouter
import com.notemess.core.ui.feature.router.FeatureNavigationRouter
import com.notemess.core.ui.theme.ThemeModeController
import com.notemess.feature.settings.AppVersion
import com.notemess.presentation.RootComponent
import com.notemess.presentation.RootComponentImpl
import org.koin.core.module.dsl.binds
import org.koin.core.module.dsl.new
import org.koin.core.module.dsl.onOptions
import org.koin.dsl.module

val appModule = module {
    single { ThemeModeController() }

    single<AppVersion> { AppVersionProvider.appVersion }

    single { AppDatabase.create(get()) }
    single { get<AppDatabase>().taskDao() }
    single { get<AppDatabase>().taskMemberDao() }
    single { get<AppDatabase>().userDao() }
    single { get<AppDatabase>().contactDao() }

    factory<FirebaseAuth> { FirebaseAuth.getInstance() }
    factory<FirebaseMessaging> { FirebaseMessaging.getInstance() }
    factory<FirebaseFirestore> {
        FirebaseFirestore.getInstance().apply {
//            firestoreSettings = firestoreSettings {
//                this.setLocalCacheSettings(persistentCacheSettings {
//                    this.setSizeBytes(1000 * 1024L * 1024L) // 1000 MB
//                })
//            }
        }
    }

    single<DecomposeComponentFactory> { KoinDecomposeComponentFactory(getKoin()) }

    single<DecomposeFeatureSerializer.Factory> { DecomposeFeatureSerializer.Factory(getAll()) }
    single<ComposeFeatureContentRenderer> { ComposeFeatureContentRendererImpl(getAll()) }
    single<FeatureComponentFactory> { DecomposeFeatureComponentFactory(getAll()) }
    single<FeatureNavigationRouter> { DecomposeFeatureNavigationRouter(get()) }.onOptions {
        binds(listOf(DecomposeFeatureNavigationRouter::class, FeatureNavigationRouter::class))
    }

    factory<RootComponent> { new(::RootComponentImpl) }
}
