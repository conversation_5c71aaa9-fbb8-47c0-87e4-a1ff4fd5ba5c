package com.notemess

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.notemess.feature.contact.DatabaseContactDao
import com.notemess.feature.contact.entity.DatabaseContactEntity
import com.notemess.feature.contact.entity.DatabaseContactWithUser
import com.notemess.feature.db.DatabaseUserDao
import com.notemess.feature.db.entity.DatabaseUserEntity
import com.notemess.feature.task.db.DatabaseTaskDao
import com.notemess.feature.task.db.entity.DatabaseTaskEntity
import com.notemess.feature.task.db.entity.DatabaseTaskMemberCrossRef
import com.notemess.feature.task.member.db.DatabaseTaskMemberDao
import com.notemess.feature.task.member.db.entity.DatabaseTaskMemberEntity
import com.notemess.feature.task.member.db.entity.DatabaseTaskMemberWithUser

@Database(
    entities = [
        DatabaseUserEntity::class,
        DatabaseContactEntity::class,
        DatabaseTaskMemberEntity::class,
        DatabaseTaskEntity::class,
        DatabaseTaskMemberCrossRef::class
    ],
    views = [
        DatabaseContactWithUser::class,
        DatabaseTaskMemberWithUser::class
    ],
    version = 1
)
internal abstract class AppDatabase : RoomDatabase() {

    abstract fun userDao(): DatabaseUserDao

    abstract fun contactDao(): DatabaseContactDao

    abstract fun taskDao(): DatabaseTaskDao

    abstract fun taskMemberDao(): DatabaseTaskMemberDao

    companion object {
        fun create(context: Context): AppDatabase {
            return Room.databaseBuilder(context, AppDatabase::class.java, "app.db")
                .build()
        }
    }
}
